<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y-WebSocket Sync Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .client {
            flex: 1;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
        }
        .client h3 {
            margin-top: 0;
            color: #333;
        }
        .editor {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            border-radius: 4px;
        }
        .status {
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            height: 100px;
            overflow-y: scroll;
            border: 1px solid #ddd;
            padding: 8px;
            font-family: monospace;
            font-size: 11px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Y-WebSocket Synchronization Test</h1>
    <p>This test creates two simulated clients to verify text synchronization works correctly.</p>
    
    <div class="container">
        <div class="client">
            <h3>Client 1</h3>
            <div id="status1" class="status disconnected">Disconnected</div>
            <textarea id="editor1" class="editor" placeholder="Type here..." disabled></textarea>
            <button id="connect1">Connect</button>
            <button id="disconnect1" disabled>Disconnect</button>
            <div class="log" id="log1"></div>
        </div>
        
        <div class="client">
            <h3>Client 2</h3>
            <div id="status2" class="status disconnected">Disconnected</div>
            <textarea id="editor2" class="editor" placeholder="Type here..." disabled></textarea>
            <button id="connect2">Connect</button>
            <button id="disconnect2" disabled>Disconnect</button>
            <div class="log" id="log2"></div>
        </div>
    </div>

    <script src="https://unpkg.com/yjs@13.6.10/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@1.5.0/dist/y-websocket.js"></script>
    
    <script>
        class TestClient {
            constructor(clientId) {
                this.clientId = clientId;
                this.provider = null;
                this.doc = null;
                this.text = null;
                this.updatingFromRemote = false;
                
                this.statusEl = document.getElementById(`status${clientId}`);
                this.editorEl = document.getElementById(`editor${clientId}`);
                this.connectBtn = document.getElementById(`connect${clientId}`);
                this.disconnectBtn = document.getElementById(`disconnect${clientId}`);
                this.logEl = document.getElementById(`log${clientId}`);
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                
                this.editorEl.addEventListener('input', (e) => {
                    if (this.text && !this.updatingFromRemote) {
                        this.handleTextChange(e);
                    }
                });
            }
            
            connect() {
                this.log('Connecting...');
                
                // Create YJS document
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');
                
                // Create WebSocket provider
                this.provider = new WebsocketProvider('ws://localhost:3000', 'test-document', this.doc);
                
                this.setupProviderListeners();
                this.setupDocumentListeners();
            }
            
            setupProviderListeners() {
                this.provider.on('status', event => {
                    this.log(`Status: ${event.status}`);
                    if (event.status === 'connected') {
                        this.updateConnectionStatus(true);
                        // Load initial content
                        setTimeout(() => {
                            this.updatingFromRemote = true;
                            this.editorEl.value = this.text.toString();
                            this.updatingFromRemote = false;
                        }, 100);
                    } else {
                        this.updateConnectionStatus(false);
                    }
                });
            }
            
            setupDocumentListeners() {
                this.text.observe(event => {
                    const textContent = this.text.toString();
                    this.log(`Text observed: "${textContent}"`);
                    
                    if (!this.updatingFromRemote) {
                        this.updatingFromRemote = true;
                        this.editorEl.value = textContent;
                        setTimeout(() => {
                            this.updatingFromRemote = false;
                        }, 0);
                    }
                });
            }
            
            handleTextChange(e) {
                const newContent = e.target.value;
                const oldContent = this.text.toString();
                
                if (oldContent !== newContent) {
                    this.log(`Local change: "${newContent}"`);
                    
                    this.doc.transact(() => {
                        if (this.text.length > 0) {
                            this.text.delete(0, this.text.length);
                        }
                        if (newContent.length > 0) {
                            this.text.insert(0, newContent);
                        }
                    });
                }
            }
            
            disconnect() {
                if (this.provider) {
                    this.provider.destroy();
                    this.provider = null;
                }
                this.updateConnectionStatus(false);
                this.log('Disconnected');
            }
            
            updateConnectionStatus(connected) {
                this.statusEl.textContent = connected ? 'Connected' : 'Disconnected';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize test clients
        const client1 = new TestClient(1);
        const client2 = new TestClient(2);
    </script>
</body>
</html>
