<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y-WebSocket Realtime Collaboration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .connected { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb;
        }
        .disconnected { 
            background-color: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .controls button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn-primary { 
            background-color: #007bff; 
            color: white; 
        }
        .btn-primary:hover { 
            background-color: #0056b3; 
        }
        .btn-secondary { 
            background-color: #6c757d; 
            color: white; 
        }
        .btn-primary:disabled { 
            background-color: #ccc; 
            cursor: not-allowed;
        }
        .editor {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s;
        }
        .editor:focus {
            outline: none;
            border-color: #007bff;
        }
        .editor:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .users {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .user {
            display: inline-block;
            margin: 3px;
            padding: 6px 12px;
            background-color: #007bff;
            color: white;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .stats {
            margin: 15px 0;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        .log {
            height: 250px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 4px;
            word-wrap: break-word;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: 500;
        }
        .log-error { color: #dc3545; }
        .log-warning { color: #fd7e14; }
        .log-success { color: #28a745; }
        h1 { color: #343a40; margin-bottom: 30px; }
        h3 { color: #495057; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>🚀 Y-WebSocket Realtime Collaboration Demo</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">Disconnected - Click Connect to start</div>
        
        <div class="controls">
            <input type="text" id="documentId" value="demo-document" placeholder="Document ID" />
            <input type="text" id="userId" value="" placeholder="User ID" />
            <button id="connectBtn" class="btn-primary">Connect</button>
            <button id="disconnectBtn" class="btn-secondary" disabled>Disconnect</button>
            <button id="syncBtn" class="btn-secondary" disabled>Force Sync</button>
        </div>
        
        <div class="users">
            <strong>👥 Connected Users:</strong>
            <div id="userList">None</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📝 Collaborative Text Editor</h3>
        <p><em>Start typing below. Open this page in multiple tabs or browsers to see real-time collaboration!</em></p>
        <p><strong>Using y-websocket for better Tiptap compatibility</strong></p>
        <textarea id="editor" class="editor" placeholder="Start typing to collaborate in real-time..." disabled></textarea>
    </div>
    
    <div class="container">
        <div class="stats">
            <strong>📊 Server Statistics:</strong>
            <div id="stats">Click Connect to load stats...</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📋 Event Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Load YJS and y-websocket from CDN -->
    <script type="module">
        import * as Y from 'https://cdn.skypack.dev/yjs@13.6.10';
        import { WebsocketProvider } from 'https://cdn.skypack.dev/y-websocket@1.4.5';
        
        window.Y = Y;
        window.WebsocketProvider = WebsocketProvider;

        // Dispatch event when libraries are loaded
        window.dispatchEvent(new CustomEvent('libraries-loaded'));
    </script>
    
    <script>
        class YWebSocketClient {
            constructor() {
                this.provider = null;
                this.doc = null;
                this.text = null;
                this.connected = false;
                this.documentId = null;
                this.userId = null;
                this.users = new Set();
                this.updatingFromRemote = false;
                
                this.initializeElements();
                this.setupEventListeners();
                this.generateUserId();
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.documentIdEl = document.getElementById('documentId');
                this.userIdEl = document.getElementById('userId');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.syncBtn = document.getElementById('syncBtn');
                this.editorEl = document.getElementById('editor');
                this.userListEl = document.getElementById('userList');
                this.statsEl = document.getElementById('stats');
                this.logEl = document.getElementById('log');
            }
            
            generateUserId() {
                this.userIdEl.value = 'user-' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.syncBtn.addEventListener('click', () => this.forceSync());

                this.editorEl.addEventListener('input', (e) => {
                    // Only handle input if we're connected, have a text object, and not updating from remote
                    if (this.text && !this.updatingFromRemote) {
                        console.log('Input event triggered - handling text change');
                        this.handleTextChange(e);
                    } else {
                        console.log('Input event ignored:', {
                            hasText: !!this.text,
                            updatingFromRemote: this.updatingFromRemote
                        });
                    }
                });
            }

            handleTextChange(e) {
                if (this.updatingFromRemote) {
                    console.log('Ignoring text change - updating from remote');
                    return;
                }

                const newContent = e.target.value;
                const oldContent = this.text.toString();

                console.log('Local text change:', { oldContent, newContent, same: oldContent === newContent });

                // Only update if content actually changed
                if (oldContent !== newContent) {
                    console.log('About to update Y.js document:', {
                        oldLength: this.text.length,
                        newLength: newContent.length,
                        newContent: newContent
                    });

                    // Apply changes to Y.js document without setting the flag
                    // The flag should only be set when we're updating FROM remote changes
                    try {
                        // Use a more efficient delta-based approach
                        this.doc.transact(() => {
                            // Clear the text completely and insert new content
                            if (this.text.length > 0) {
                                this.text.delete(0, this.text.length);
                            }

                            // Insert new content if not empty
                            if (newContent.length > 0) {
                                this.text.insert(0, newContent);
                            }

                            console.log('After Y.js update:', {
                                textLength: this.text.length,
                                textContent: this.text.toString(),
                                expectedContent: newContent,
                                contentMatch: this.text.toString() === newContent
                            });
                        });

                        this.log('📤 Local text change sent to document');
                    } catch (error) {
                        console.error('Error updating Y.js document:', error);
                        this.log('❌ Error sending local change', 'error');
                    }
                }
            }

            connect() {
                if (typeof Y === 'undefined' || typeof WebsocketProvider === 'undefined') {
                    this.log('❌ Required libraries not loaded!', 'error');
                    return;
                }
                
                const documentId = this.documentIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.documentId = documentId;
                this.userId = userId;
                
                this.log('🔌 Connecting to y-websocket server...');
                
                // Initialize YJS document
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');
                
                // Create WebSocket provider
                const wsUrl = `ws://localhost:3000`;
                this.provider = new WebsocketProvider(wsUrl, documentId, this.doc);
                
                this.setupProviderListeners();
                this.setupDocumentListeners();
            }
            
            setupProviderListeners() {
                this.provider.on('status', event => {
                    this.log(`📡 Connection status: ${event.status}`);
                    if (event.status === 'connected') {
                        this.updateConnectionStatus(true);
                        this.log('✅ Connected to y-websocket server', 'success');
                        this.startStatsUpdates();

                        // Update editor with current document content
                        setTimeout(() => {
                            this.updatingFromRemote = true;
                            this.editorEl.value = this.text.toString();
                            this.updatingFromRemote = false;
                            this.log('📄 Initial document content loaded');
                        }, 100);
                    } else if (event.status === 'disconnected') {
                        this.updateConnectionStatus(false);
                        this.log('❌ Disconnected from server', 'error');
                    }
                });

                this.provider.on('connection-error', error => {
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });

                // Handle awareness (user presence)
                this.provider.awareness.on('change', changes => {
                    this.updateUserList();
                    this.log(`👥 Awareness updated: ${changes.added.length} added, ${changes.removed.length} removed`);
                });

                // Set local awareness state
                this.provider.awareness.setLocalStateField('user', {
                    name: this.userId,
                    color: this.generateUserColor()
                });
            }

            setupDocumentListeners() {
                // Listen to text changes - this is the primary way to handle remote updates
                this.text.observe(event => {
                    console.log('Text observed:', event);
                    // Get the actual text content using supported methods
                    const textContent = this.text.toString();

                    console.log('Text details:', {
                        length: this.text.length,
                        content: textContent,
                        contentLength: textContent.length,
                        updatingFromRemote: this.updatingFromRemote,
                        eventChanges: event.changes,
                        eventDelta: event.delta
                    });

                    // Check if this change originated from the provider (remote)
                    // We should update the editor for remote changes only
                    if (!this.updatingFromRemote) {
                        console.log('Updating editor from remote change');
                        console.log('Setting editor value to:', textContent);

                        // Set flag to prevent input event from triggering during update
                        this.updatingFromRemote = true;

                        const cursorPosition = this.editorEl.selectionStart;
                        this.editorEl.value = textContent;

                        // Try to maintain cursor position
                        try {
                            this.editorEl.setSelectionRange(cursorPosition, cursorPosition);
                        } catch (e) {
                            // Ignore cursor positioning errors
                        }

                        // Reset flag after DOM update
                        setTimeout(() => {
                            this.updatingFromRemote = false;
                        }, 0);

                        this.log('📥 Document updated from remote via text.observe');
                    } else {
                        console.log('Ignoring text.observe - currently updating from remote');
                    }
                });

                // Listen for all document updates for debugging
                this.doc.on('update', (update, origin) => {
                    console.log('Document update:', {
                        updateSize: update.length,
                        origin: origin === this.provider ? 'provider' : origin === null ? 'local' : 'other',
                        text: this.text.toString()
                    });
                    this.log(`📡 Document update: ${origin === this.provider ? 'from remote' : 'local'}`);
                });
            }

            generateUserColor() {
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
                return colors[Math.floor(Math.random() * colors.length)];
            }
            
            disconnect() {
                if (this.provider) {
                    this.provider.destroy();
                    this.provider = null;
                }
                this.updateConnectionStatus(false);
                this.log('🔌 Disconnected by user');
            }

            forceSync() {
                if (!this.text) return;

                this.updatingFromRemote = true;
                this.editorEl.value = this.text.toString();
                this.updatingFromRemote = false;
                this.log('🔄 Forced sync from document state');
                console.log('Force sync - Document text:', this.text.toString());
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 
                    `Connected to ${this.documentId} as ${this.userId}` : 
                    'Disconnected - Click Connect to start';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.syncBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
                
                if (!connected) {
                    this.users.clear();
                    this.updateUserList();
                    clearInterval(this.statsInterval);
                }
            }
            
            updateUserList() {
                if (!this.provider) {
                    this.userListEl.textContent = 'None';
                    return;
                }

                const awarenessStates = this.provider.awareness.getStates();
                this.users.clear();
                
                awarenessStates.forEach((state, clientId) => {
                    if (state.user && state.user.name) {
                        this.users.add(state.user.name);
                    }
                });

                if (this.users.size === 0) {
                    this.userListEl.textContent = 'None';
                } else {
                    this.userListEl.innerHTML = '';
                    this.users.forEach(userId => {
                        const userEl = document.createElement('span');
                        userEl.className = 'user';
                        userEl.textContent = userId;
                        this.userListEl.appendChild(userEl);
                    });
                }
            }
            
            startStatsUpdates() {
                this.fetchStats();
                this.statsInterval = setInterval(() => {
                    this.fetchStats();
                }, 5000);
            }
            
            fetchStats() {
                fetch('http://localhost:3000/api/stats')
                    .then(response => response.json())
                    .then(stats => {
                        this.statsEl.innerHTML = `
                            <div>📊 Total Connections: ${stats.connections?.totalConnections || 0}</div>
                            <div>📄 Active Documents: ${stats.documents?.totalDocuments || 0}</div>
                            <div>🕒 Last Updated: ${new Date(stats.timestamp).toLocaleTimeString()}</div>
                        `;
                    })
                    .catch(error => {
                        this.log(`Failed to fetch stats: ${error.message}`, 'warning');
                    });
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const timestampSpan = document.createElement('span');
                timestampSpan.className = 'log-timestamp';
                timestampSpan.textContent = `[${timestamp}] `;
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                messageSpan.className = `log-${type}`;
                
                logEntry.appendChild(timestampSpan);
                logEntry.appendChild(messageSpan);
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize the client after libraries load
        let client;

        function initializeClient() {
            client = new YWebSocketClient();
            client.log('🚀 Y-WebSocket Client loaded');

            // Test server
            fetch('http://localhost:3000/health')
                .then(response => response.json())
                .then(data => {
                    client.log(`✅ Server health: ${data.status}`, 'success');
                })
                .catch(error => {
                    client.log(`❌ Server not reachable: ${error.message}`, 'error');
                    client.log('Make sure the server is running: npm start', 'warning');
                });
        }

        // Wait for libraries to load
        window.addEventListener('libraries-loaded', initializeClient);

        // Fallback for page load
        window.addEventListener('load', () => {
            if (typeof Y !== 'undefined' && typeof WebsocketProvider !== 'undefined' && !client) {
                initializeClient();
            }
        });

    </script>
</body>
</html>
